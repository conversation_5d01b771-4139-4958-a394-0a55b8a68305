import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Navbar from "../components/layout/Navbar";
import { novelAPI } from "../services/api";

const NovelDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [novel, setNovel] = useState(null);
  const [comments, setComments] = useState([]);
  const [commentText, setCommentText] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // 获取用户信息
    const userData = localStorage.getItem("user");
    if (userData) {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      setIsAdmin(parsedUser.is_admin);
    }

    fetchNovel();
    fetchComments();
  }, [id]);

  const fetchNovel = async () => {
    try {
      setLoading(true);
      const response = await novelAPI.getNovelById(id);
      setNovel(response.data);
    } catch (err) {
      setError("获取小说详情失败");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    try {
      const response = await novelAPI.getNovelComments(id);
      setComments(response.data);
    } catch (err) {
      console.error("获取评论失败", err);
    }
  };

  const handleDelete = async () => {
    if (window.confirm("确定要删除这篇小说吗？")) {
      try {
        await novelAPI.deleteNovel(id);
        navigate("/novels");
      } catch (err) {
        setError(err.response?.data?.msg || "删除失败");
      }
    }
  };

  const handleCommentSubmit = async (e) => {
    e.preventDefault();
    if (!commentText.trim()) return;

    try {
      await novelAPI.addComment(id, { content: commentText });
      setCommentText("");
      fetchComments(); // 重新获取评论列表
    } catch (err) {
      setError(err.response?.data?.msg || "评论失败");
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (window.confirm("确定要删除这条评论吗？")) {
      try {
        await novelAPI.deleteComment(id, commentId);
        fetchComments(); // 重新获取评论列表
      } catch (err) {
        setError(err.response?.data?.msg || "删除评论失败");
      }
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">加载中...</span>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="alert alert-danger">{error}</div>
        </div>
      </>
    );
  }

  if (!novel) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="alert alert-warning">小说不存在</div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container mt-5">
        <div className="card mb-4">
          <div className="card-header bg-light d-flex justify-content-between align-items-center">
            <h2>{novel.title}</h2>
            {(isAdmin || (user && user.id === novel.user_id)) && (
              <div>
                <button className="btn btn-danger me-2" onClick={handleDelete}>
                  删除
                </button>
                {user && user.id === novel.user_id && (
                  <button
                    className="btn btn-primary"
                    onClick={() => navigate(`/editor/${novel.id}`)}
                  >
                    编辑
                  </button>
                )}
              </div>
            )}
          </div>
          <div className="card-body">
            <div className="mb-4">
              <small className="text-muted">
                作者: {novel.author} | 发布于:{" "}
                {new Date(novel.created_at).toLocaleString()}
              </small>
            </div>
            <div className="novel-content" style={{ whiteSpace: "pre-line" }}>
              {novel.content}
            </div>
          </div>
        </div>

        {/* 评论区 */}
        <div className="card">
          <div className="card-header bg-light">
            <h4>评论区</h4>
          </div>
          <div className="card-body">
            {/* 评论列表 */}
            {comments.length === 0 ? (
              <p className="text-muted">暂无评论</p>
            ) : (
              <div className="mb-4">
                {comments.map((comment) => (
                  <div key={comment.id} className="border-bottom pb-3 mb-3">
                    <div className="d-flex justify-content-between">
                      <h6>{comment.username}</h6>
                      {(isAdmin || (user && user.id === comment.user_id)) && (
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => handleDeleteComment(comment.id)}
                        >
                          删除
                        </button>
                      )}
                    </div>
                    <p>{comment.content}</p>
                    <small className="text-muted">
                      {new Date(comment.created_at).toLocaleString()}
                    </small>
                  </div>
                ))}
              </div>
            )}

            {/* 评论表单 - 仅管理员可见 */}
            {isAdmin && (
              <form onSubmit={handleCommentSubmit}>
                <div className="mb-3">
                  <label htmlFor="comment" className="form-label">
                    管理员评价
                  </label>
                  <textarea
                    className="form-control"
                    id="comment"
                    rows="3"
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    required
                  ></textarea>
                </div>
                <button type="submit" className="btn btn-primary">
                  提交评价
                </button>
              </form>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default NovelDetail;
