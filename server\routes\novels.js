const express = require("express");
const router = express.Router();
const novelController = require("../controllers/novelController");
const commentController = require("../controllers/commentController");
const auth = require("../middleware/auth");

// 获取所有小说
router.get("/", novelController.getAllNovels);

// 获取单个小说
router.get("/:id", novelController.getNovelById);

// 获取当前用户的小说 (需要认证)
router.get("/user/me", auth, novelController.getUserNovels);

// 创建小说 (需要认证)
router.post("/", auth, novelController.createNovel);

// 更新小说 (需要认证)
router.put("/:id", auth, novelController.updateNovel);

// 删除小说 (需要认证)
router.delete("/:id", auth, novelController.deleteNovel);

// 获取小说的评论
router.get("/:novelId/comments", commentController.getNovelComments);

// 添加评论 (需要认证)
router.post("/:novelId/comments", auth, commentController.addComment);

// 删除评论 (需要认证)
router.delete(
  "/:novelId/comments/:commentId",
  auth,
  commentController.deleteComment
);

module.exports = router;
