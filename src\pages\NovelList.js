import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Navbar from "../components/layout/Navbar";
import { novelAPI } from "../services/api";

const NovelList = () => {
  const [novels, setNovels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchNovels();
  }, []);

  const fetchNovels = async () => {
    try {
      setLoading(true);
      const response = await novelAPI.getAllNovels();
      setNovels(response.data);
    } catch (err) {
      setError("获取小说列表失败");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">加载中...</span>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container mt-5">
        <h2 className="mb-4">小说作品展示</h2>
        {error && <div className="alert alert-danger">{error}</div>}
        {novels.length === 0 ? (
          <div className="alert alert-info">暂无小说作品</div>
        ) : (
          <div className="row">
            {novels.map((novel) => (
              <div className="col-md-4 mb-4" key={novel.id}>
                <div className="card h-100">
                  <div className="card-body">
                    <h5 className="card-title">{novel.title}</h5>
                    <h6 className="card-subtitle mb-2 text-muted">
                      作者: {novel.author}
                    </h6>
                    <p className="card-text">
                      {novel.content.length > 100
                        ? `${novel.content.substring(0, 100)}...`
                        : novel.content}
                    </p>
                  </div>
                  <div className="card-footer bg-white border-top-0">
                    <small className="text-muted">
                      发布于: {new Date(novel.created_at).toLocaleDateString()}
                    </small>
                    <Link
                      to={`/novel/${novel.id}`}
                      className="btn btn-sm btn-primary float-end"
                    >
                      阅读全文
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default NovelList;
